{"name": "voice-journal", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@vitejs/plugin-react-refresh": "^1.3.6", "vite": "^6.3.4"}}